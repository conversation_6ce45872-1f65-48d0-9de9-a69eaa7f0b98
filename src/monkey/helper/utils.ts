/**
 * 判断是否支持安全区
 * @returns {boolean}
*/
const hasSafeArea = (): boolean => {
  const safeArea = uni.getSystemInfoSync();
  return safeArea.safeAreaInsets.bottom > 0 ? true : false;
};

/**
 * 获取导航栏背景颜色
 * @param scrollTop 滚动距离
 * @returns 导航栏背景颜色
 */
const getNavBarBgColor = (scrollTop: number): string => {
  const threshold = 200;
  if (scrollTop > threshold) {
    return `rgba(0, 109, 187, ${scrollTop / threshold})`;
  }
  return `rgba(0, 109, 187, 1)`;
};

/**
 * 获取导航栏标题颜色
 * @param scrollTop 滚动距离
 * @returns 导航栏标题颜色
 */
const getNavTitleColor = (scrollTop: number): string => {
  const threshold = 200;
  if (scrollTop > threshold) {
    return `rgba(0, 0, 0, ${scrollTop / threshold})`;
  }
  return `rgba(255, 255, 255,1)`;
};


const utils = {
  hasSafeArea,
  getNavBarBgColor,
  getNavTitleColor
};

export default utils;
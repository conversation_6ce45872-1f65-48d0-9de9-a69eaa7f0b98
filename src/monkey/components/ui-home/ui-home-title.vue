<template>
  <div class="flex items-center justify-between">
    <div class="relative text-base font-bold">
      <span class="relative text-gray-800">
        {{ props.title }}
        <span class="absolute bottom-0.5 left-1.5 w-12 h-1 bg-blue-500/55 rounded-full"></span>
      </span>
    </div>
    <div v-if="props.more" class="flex items-center justify-center gap-1 text-xs text-gray-500 cursor-pointer px-2 py-1 rounded-md" @click="emit('more')">
      <div>{{ props.more }}</div>
      <uni-icons type="arrowright" size="14" color="#999"></uni-icons>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps<{
  title: string
  more?: string
}>()

const emit = defineEmits<{
  (e: 'more'): void
}>()

const handleMore = () => {
  emit('more')
}
</script>
<style scoped>
/* 使用Tailwind实现了所有样式，此处保留空样式块 */
</style>
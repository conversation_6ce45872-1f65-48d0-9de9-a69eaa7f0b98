<template>
  <div class="rounded-lg bg-white shadow-md p-4 py-5">
    <!-- 实时数据标题 -->
    <!-- <div class="border-b border-gray-100 pb-2 mb-3 flex items-center justify-between">
      <div class="flex items-center">
        <div class="w-1 h-3 bg-blue-500 mr-2 rounded-full"></div>
        <div class="text-sm font-medium text-gray-800">实时数据更新中</div>
      </div>
    </div> -->
    <slot name="title"></slot>
    <!-- 主要数据展示 - 更精致简洁 -->
    <div class="grid grid-cols-2 gap-4 mb-4 mt-2">
      <div class="text-center border-r border-gray-100">
        <div class="text-xl font-bold text-blue-600 mb-1 flex items-center justify-center">
          <span class="mr-1">74,047</span>
          <span class="text-xs text-blue-400">↑</span>
        </div>
        <div class="text-xs text-gray-500">累计交易额 (万元)</div>
      </div>
      <div class="text-center">
        <div class="text-xl font-bold text-blue-600 mb-1 flex items-center justify-center">
          <span class="mr-1">10,940</span>
          <span class="text-xs text-blue-400">↑</span>
        </div>
        <div class="text-xs text-gray-500">累计交易量 (吨)</div>
      </div>
    </div>

    <!-- 数据统计 - 更紧凑 -->
    <div class="grid grid-cols-3 gap-3 mb-4 px-1 py-2 bg-gray-50 rounded">
      <div class="text-center">
        <div class="text-lg font-medium text-gray-700 mb-0.5">1,167</div>
        <div class="text-xs text-gray-500">订单数量</div>
      </div>
      <div class="text-center border-x border-gray-100">
        <div class="text-lg font-medium text-gray-700 mb-0.5">122</div>
        <div class="text-xs text-gray-500">上线品种</div>
      </div>
      <div class="text-center">
        <div class="text-lg font-medium text-gray-700 mb-0.5">6,239</div>
        <div class="text-xs text-gray-500">注册商家</div>
      </div>
    </div>

    <!-- 交易动态 - 更精致 -->
    <div class="border-t border-gray-100 pt-2 mt-2">
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center">
          <div class="w-1 h-3 bg-blue-500 mr-2 rounded-full"></div>
          <div class="text-xs font-medium text-gray-700">实时交易动态</div>
        </div>
      </div>
      <div class="space-y-1.5">
        <div class="flex items-center text-xs">
          <div class="w-1 h-6 bg-gradient-to-b from-blue-400 to-transparent rounded-full mr-2"></div>
          <div class="flex-1">
            <div class="flex justify-between text-gray-600">
              <span class="truncate">张**采购花生139kg</span>
              <span class="text-blue-500 ml-1 whitespace-nowrap">￥12.07万</span>
            </div>
            <div class="flex justify-between text-gray-400">
              <span>刚刚</span>
              <span class="bg-blue-50 text-blue-400 px-1 rounded text-2xs">已成交</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
  .text-2xs {
    font-size: 0.65rem;
  }
</style>

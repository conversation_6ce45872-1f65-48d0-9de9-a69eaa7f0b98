<template>
  <div class="flex overflow-x-auto pb-2 space-x-4">
    <div class="rounded-lg border border-blue-200 flex-shrink-0 w-28 p-4 bg-white shadow-sm active:shadow-md transition-shadow cursor-pointer">
      <div class="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white mb-3 mx-auto"></div>
      <div class="text-center">
        <div class="text-sm font-medium text-gray-800 mb-1">扫码入库</div>
        <div class="text-xs text-gray-500">快速登记</div>
      </div>
    </div>
    <div class="rounded-lg border border-sky-200 flex-shrink-0 w-28 p-4 bg-white shadow-sm active:shadow-md transition-shadow cursor-pointer">
      <div class="h-12 w-12 bg-gradient-to-br from-sky-500 to-sky-600 rounded-xl flex items-center justify-center text-white mb-3 mx-auto"></div>
      <div class="text-center">
        <div class="text-sm font-medium text-gray-800 mb-1">批量出库</div>
        <div class="text-xs text-gray-500">批量处理</div>
      </div>
    </div>
    <div class="rounded-lg border border-indigo-200 flex-shrink-0 w-28 p-4 bg-white shadow-sm active:shadow-md transition-shadow cursor-pointer">
      <div class="h-12 w-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center text-white mb-3 mx-auto"></div>
      <div class="text-center">
        <div class="text-sm font-medium text-gray-800 mb-1">实时盘点</div>
        <div class="text-xs text-gray-500">库存核查</div>
      </div>
    </div>
    <div class="rounded-lg border border-blue-300 flex-shrink-0 w-28 p-4 bg-white shadow-sm active:shadow-md transition-shadow cursor-pointer">
      <div class="h-12 w-12 bg-gradient-to-br from-blue-600 to-sky-700 rounded-xl flex items-center justify-center text-white mb-3 mx-auto"></div>
      <div class="text-center">
        <div class="text-sm font-medium text-gray-800 mb-1">数据报表</div>
        <div class="text-xs text-gray-500">分析统计</div>
      </div>
    </div>
    <div class="rounded-lg border border-sky-300 flex-shrink-0 w-28 p-4 bg-white shadow-sm active:shadow-md transition-shadow cursor-pointer">
      <div class="h-12 w-12 bg-gradient-to-br from-sky-600 to-indigo-600 rounded-xl flex items-center justify-center text-white mb-3 mx-auto"></div>
      <div class="text-center">
        <div class="text-sm font-medium text-gray-800 mb-1">仓库地图</div>
        <div class="text-xs text-gray-500">位置导航</div>
      </div>
    </div>
  </div>
</template>

<script setup>

</script>

<style lang="scss" scoped>
</style>
<template>
  <div class="arc-background"></div>
</template>
<script setup lang="ts"></script>
<style scoped lang="scss">
  .arc-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 350px;
    background: linear-gradient(to bottom, rgb(0, 109, 187), rgb(0, 109, 187) 20%, rgba(0, 128, 212, 0.95) 50%, rgba(77, 166, 224, 0.7) 80%, rgba(77, 166, 224, 0) 100%);
    z-index: 0;
  }

  .arc-background::after {
    content: '';
    position: absolute;
    bottom: -40px;
    left: 0;
    width: 100%;
    height: 80px;
    background: radial-gradient(ellipse at center, rgba(77, 166, 224, 0.3) 0%, rgba(77, 166, 224, 0) 70%);
    border-radius: 50% 50% 0 0 / 100% 100% 0 0;
  }
</style>

<template>
  <ly-layout is-arc-bg>
    <!-- 顶部用户信息区域 -->
    <div class="header relative z-20 text-white px-4 pb-4" :style="{ backgroundColor: navBarBgColor }">
      <ly-status-bar />
      <div class="flex items-center justify-between mb-5 mt-8">
        <div class="flex items-center">
          <div class="relative h-14 w-14 bg-white/20 rounded-full flex items-center justify-center mr-3 backdrop-blur-sm border border-white/30 shadow-xl">
            <div class="h-10 w-10 bg-white/30 rounded-full flex items-center justify-center">
              <div class="text-lg">❄️</div>
            </div>
            <!-- 在线状态指示器 -->
            <div class="absolute -bottom-0.5 -right-0.5 h-4 w-4 bg-green-400 rounded-full border-2 border-white shadow-md">
              <div class="h-full w-full bg-green-400 rounded-full animate-ping"></div>
            </div>
          </div>
          <div>
            <div class="text-lg font-bold mb-0.5">{{ userInfo.name }}</div>
            <div class="flex items-center text-xs text-blue-100">
              <div class="bg-orange-500/80 px-2 py-0.5 rounded-full text-xs flex items-center gap-1">
                <text class="i-mdi-star-minus-outline text-base text-white"></text>
                未认证
              </div>
              <!-- i-mdi-star-plus -->
              <div class="mx-2">•</div>
              <div>{{ userInfo.department }}</div>
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <!-- <div class="h-8 w-8 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-md">
            <div class="text-sm">🔔</div>
          </div> -->
          <div class="h-8 w-8 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-md">
            <uni-icons type="gear-filled" size="20" color="#fff"></uni-icons>
          </div>
        </div>
      </div>

      <!-- 认证状态卡片 -->
      <ui-user-verify @verify="handleVerify" />
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 px-4 relative z-10">
      <!-- 冷库业务概览 -->
      <div class="mb-4">
        <ui-user-bo @allOrders="handleAllOrders" />
      </div>

      <!-- 快捷功能 -->
      <div class="bg-white rounded-xl p-4 shadow-lg border border-gray-100 mb-4">
        <div class="flex items-center mb-4">
          <div class="h-6 w-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-2">
            <div class="text-xs text-white">⚡</div>
          </div>
          <div class="text-base font-bold text-gray-800">快捷功能</div>
        </div>
        <div class="grid grid-cols-2 gap-3">
          <div v-for="service in quickServices" :key="service.name" class="flex items-center p-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-100 hover:shadow-md transition-all">
            <div class="h-8 w-8 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg flex items-center justify-center text-white mr-3 shadow-sm">
              <text :class="service.icon"></text>
            </div>
            <div class="flex-1 min-w-0">
              <div class="font-medium text-gray-800 text-sm">
                {{ service.name }}
              </div>
              <div class="text-xs text-gray-500 truncate">
                {{ service.desc }}
              </div>
            </div>
            <div class="text-[#006dbb] text-xs">→</div>
          </div>
        </div>
      </div>

      <!-- 冷库监控中心 -->
      <div class="bg-white rounded-xl p-4 shadow-lg border border-gray-100 mb-4">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <div class="h-6 w-6 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg flex items-center justify-center mr-2">
              <div class="text-xs text-white">🌡️</div>
            </div>
            <div class="text-base font-bold text-gray-800">冷库监控</div>
          </div>
          <div class="flex items-center text-xs text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full">
            <div class="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-1 animate-pulse"></div>
            <span>实时监控</span>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-3">
          <div v-for="monitor in monitoringData" :key="monitor.name" class="p-3 bg-gradient-to-br from-gray-50 to-cyan-50 rounded-lg border border-gray-100">
            <div class="flex items-center justify-between mb-2">
              <div class="h-7 w-7 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center text-white text-xs">
                {{ monitor.icon }}
              </div>
              <div class="text-xs" :class="monitor.statusColor">
                {{ monitor.status }}
              </div>
            </div>
            <div class="font-medium text-gray-800 text-sm">
              {{ monitor.name }}
            </div>
            <div class="text-xs text-gray-500 mt-0.5">{{ monitor.value }}</div>
          </div>
        </div>
      </div>

      <!-- 仓储服务 -->
      <div class="bg-white rounded-xl p-4 shadow-lg border border-gray-100 mb-6">
        <div class="flex items-center mb-4">
          <div class="h-6 w-6 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center mr-2">
            <div class="text-xs text-white">📦</div>
          </div>
          <div class="text-base font-bold text-gray-800">仓储服务</div>
        </div>
        <div class="space-y-3">
          <div v-for="storage in storageServices" :key="storage.name" class="flex items-center p-3 bg-gradient-to-r from-gray-50 to-indigo-50 rounded-lg border border-gray-100 hover:shadow-md transition-all">
            <div class="h-8 w-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center text-white mr-3 shadow-sm">
              <div class="text-xs">{{ storage.icon }}</div>
            </div>
            <div class="flex-1">
              <div class="font-medium text-gray-800 text-sm">
                {{ storage.name }}
              </div>
              <div class="text-xs text-gray-500">{{ storage.desc }}</div>
            </div>
            <div class="flex items-center">
              <div class="text-xs text-[#006dbb] bg-blue-50 px-2 py-0.5 rounded-full mr-2">
                {{ storage.status }}
              </div>
              <div class="text-[#006dbb] text-xs">→</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  // 导入配置
  import monkey from '@/monkey';

  // 导航栏背景颜色
  const navBarBgColor: Ref<string> = ref('rgba(0, 109, 187, 0)');

  /**
   * 处理认证
   * @param type 认证类型
   */
  const handleVerify = (type: 'company' | 'person') => {
    monkey.$router.navigateTo(`/modules/user/auth/${type}/index`);
  };

  /**
   * 处理订单
   * @param type 订单类型
   */
  const handleAllOrders = ({ type, status }: { type: string; status: number }) => {
    monkey.$router.navigateTo(`/modules/order/${type}/index?status=${status}`);
  };

  // 用户信息
  const userInfo = reactive({
    name: '张经理',
    role: '冷库主管',
    department: '运营部',
  });

  // 月度统计
  const monthlyStats = reactive({
    purchase: '156',
    sales: '142',
  });

  // 快捷功能
  const quickServices = reactive([
    {
      name: '地址管理',
      desc: '我的收货地址',
      icon: 'i-mdi-map-marker-outline',
    },
    {
      name: '客户档案',
      desc: '客户信息管理',
      icon: '👥',
    },
    {
      name: '财务报表',
      desc: '收支统计分析',
      icon: '📊',
    },
    {
      name: '质检报告',
      desc: '商品质量检测',
      icon: '🔍',
    },
  ]);

  // 冷库监控数据
  const monitoringData = reactive([
    {
      name: '1号冷库',
      icon: '🌡️',
      value: '-18°C | 65%RH',
      status: '正常',
      statusColor: 'text-emerald-600',
    },
    {
      name: '2号冷库',
      icon: '🌡️',
      value: '-20°C | 68%RH',
      status: '正常',
      statusColor: 'text-emerald-600',
    },
    {
      name: '制冷系统',
      icon: '❄️',
      value: '运行中',
      status: '良好',
      statusColor: 'text-blue-600',
    },
    {
      name: '电力系统',
      icon: '⚡',
      value: '380V 稳定',
      status: '正常',
      statusColor: 'text-emerald-600',
    },
  ]);

  // 仓储服务
  const storageServices = reactive([
    {
      name: '冷库监控',
      desc: '实时温度湿度监控',
      icon: '🌡️',
      status: '正常',
    },
    {
      name: '库存管理',
      desc: '货物进出库管理',
      icon: '📦',
      status: '运行中',
    },
    {
      name: '预约服务',
      desc: '入库出库预约',
      icon: '📅',
      status: '可用',
    },
    {
      name: '质检报告',
      desc: '货物质量检测',
      icon: '🔍',
      status: '最新',
    },
  ]);
</script>

<style lang="scss" scoped></style>

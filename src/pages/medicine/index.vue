<template>
  <ly-layout>
    <div class="p-4">
      <!-- 分类选择器 -->
      <div class="mb-4 overflow-x-auto hide-scrollbar">
        <div class="flex space-x-2 pb-1">
          <div
            v-for="(category, index) in categories"
            :key="index"
            class="px-3 py-1.5 rounded-full text-xs whitespace-nowrap cursor-pointer transition-all duration-200"
            :class="{
              'bg-red-500 text-white shadow-sm': index === currentCategory,
              'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:-translate-y-0.5': index !== currentCategory,
            }"
            @click="currentCategory = index"
          >
            {{ category }}
          </div>
        </div>
      </div>

      <!-- 药材市场数据表格 -->
      <div class="space-y-2">
        <!-- 表头 -->
        <div class="grid grid-cols-5 gap-2 border-b border-gray-100 bg-white rounded-t-lg py-4 shadow-sm">
          <div class="text-xs font-medium text-gray-500 text-center">图片</div>
          <div class="text-xs font-medium text-gray-500 text-center">品名/规格</div>
          <div class="text-xs font-medium text-gray-500 text-center">产地</div>
          <div class="text-xs font-medium text-gray-500 text-center">价格</div>
          <div class="text-xs font-medium text-gray-500 text-center">涨跌幅</div>
        </div>
        <!-- 数据行 -->
        <div class="space-y-3">
          <div v-for="(item, index) in medicineList" :key="index" class="grid grid-cols-5 gap-2 items-center py-4 bg-white rounded-lg shadow-sm transition-transform duration-200" :style="{ '--index': index }" @click="handleMedicineClick(item)">
            <div class="rounded-full flex items-center justify-center">
              <img :src="`https://picsum.photos/200/300?id=${index}`" alt="药材" class="w-8 h-8 object-cover rounded-full" />
            </div>
            <div class="flex items-center flex-col gap-1 justify-center">
              <div class="text-sm text-gray-800 font-medium text-center">{{ item.name }}</div>
              <div class="text-xs text-gray-500 text-center">{{ item.spec }}</div>
            </div>
            <div class="text-xs text-gray-600 text-center">{{ item.origin }}</div>
            <div class="text-sm font-bold text-center text-red-500">¥{{ item.price }}</div>
            <div class="flex justify-center">
              <span
                class="inline-block px-2 py-1 rounded-full text-xs font-medium"
                :class="{
                  'text-green-600 bg-green-50': isPositive(item.change),
                  'text-red-600 bg-red-50': isNegative(item.change),
                  'text-gray-500 bg-gray-50': isZero(item.change),
                }"
              >
                {{ item.change }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  // 当前选择的分类
  const currentCategory = ref(0);

  // 药材分类
  const categories = ['全部', '补气类', '补血类', '清热类', '解表类', '祛湿类', '安神类', '活血类'];

  // 当前选中的药材
  const selectedHerb = ref('');

  // 热门药材列表
  const hotHerbs = ['川芎', '党参', '白术', '黄芪', '三七', '丹参', '太子参', '红参', '西洋参'];

  // 药材数据列表
  const medicineList = ref([
    {
      id: '1',
      name: '川芎',
      spec: '统货',
      origin: '四川产区',
      price: 45.8,
      change: '+4.5%',
      inventory: 1200,
      category: '活血类',
    },
    {
      id: '2',
      name: '党参',
      spec: '统货',
      origin: '甘肃岷县',
      price: 62.5,
      change: '-1.2%',
      inventory: 850,
      category: '补气类',
    },
    {
      id: '3',
      name: '白术',
      spec: '一级',
      origin: '浙江产区',
      price: 38.2,
      change: '0.0%',
      inventory: 2000,
      category: '补气类',
    },
    {
      id: '4',
      name: '黄芪',
      spec: '特级',
      origin: '内蒙古',
      price: 72.5,
      change: '+2.3%',
      inventory: 1500,
      category: '补气类',
    },
    {
      id: '5',
      name: '三七',
      spec: '20头',
      origin: '云南文山',
      price: 435.0,
      change: '+5.2%',
      inventory: 300,
      category: '活血类',
    },
    {
      id: '6',
      name: '丹参',
      spec: '特级',
      origin: '山西产区',
      price: 28.5,
      change: '-0.8%',
      inventory: 2200,
      category: '活血类',
    },
    {
      id: '7',
      name: '太子参',
      spec: '一级',
      origin: '浙江温州',
      price: 120.0,
      change: '+1.5%',
      inventory: 980,
      category: '补气类',
    },
    {
      id: '8',
      name: '红参',
      spec: '特级',
      origin: '吉林',
      price: 680.0,
      change: '+3.2%',
      inventory: 500,
      category: '补气类',
    },
    {
      id: '9',
      name: '西洋参',
      spec: '特级',
      origin: '加拿大',
      price: 320.0,
      change: '-2.1%',
      inventory: 750,
      category: '补气类',
    },
    {
      id: '10',
      name: '陈皮',
      spec: '10年',
      origin: '广东新会',
      price: 180.0,
      change: '+0.5%',
      inventory: 1200,
      category: '理气类',
    },
  ]);

  // 过滤后的药材列表
  const filteredMedicines = computed(() => {
    let filtered = medicineList.value;

    // 按分类过滤
    if (currentCategory.value > 0) {
      const categoryName = categories.value[currentCategory.value].name;
      filtered = filtered.filter((medicine) => medicine.category === categoryName);
    }

    // 按搜索关键词过滤
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase().trim();
      filtered = filtered.filter((medicine) => medicine.name.toLowerCase().includes(query) || medicine.origin.toLowerCase().includes(query) || medicine.spec.toLowerCase().includes(query));
    }

    // 排序
    switch (currentSort.value) {
      case 'price-asc':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-desc':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'change-desc':
        filtered.sort((a, b) => {
          const aChange = parseFloat(a.change.replace('%', '').replace('+', ''));
          const bChange = parseFloat(b.change.replace('%', '').replace('+', ''));
          return bChange - aChange;
        });
        break;
      default:
        // 保持原始顺序
        break;
    }

    return filtered;
  });

  // 判断涨跌幅是否为正数
  const isPositive = (change: string) => change.startsWith('+') && change !== '+0.0%';

  // 判断涨跌幅是否为负数
  const isNegative = (change: string) => change.startsWith('-');

  // 判断涨跌幅是否为零
  const isZero = (change: string) => change === '0.0%' || change === '+0.0%';

  // 药材点击事件
  const handleMedicineClick = (medicine: any) => {
    // 跳转到药材详情页
    monkey.$router.navigateTo(`/modules/home/<USER>/detail?id=${medicine.id}`);
  };

  // 选择热门药材
  const selectHotHerb = (herb: any) => {
    searchQuery.value = herb.name;
  };
</script>

<style lang="scss" scoped></style>

<template>
  <ly-layout isArcBg>
    <!-- 顶部信息区域 -->
    <div class="relative z-10 px-4 pt-4 pb-6">
      <div class="flex items-center justify-between mb-4">
        <div class="text-xl font-bold text-white">仓库详情</div>
        <div class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-xs text-white flex items-center">
          <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1 animate-pulse"></div>
          <span>在线监控</span>
        </div>
      </div>

      <!-- 仓库信息卡片 -->
      <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
        <div class="flex items-center mb-3">
          <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <path d="M16 10a4 4 0 0 1-8 0"></path>
            </svg>
          </div>
          <div>
            <div class="text-base font-bold text-gray-800">杭州中心仓库</div>
            <div class="text-xs text-gray-500">浙江省杭州市余杭区创新路100号</div>
          </div>
          <div class="ml-auto bg-green-100 text-green-600 px-2 py-1 rounded-md text-xs font-medium">
            正常运营
          </div>
        </div>

        <div class="grid grid-cols-4 gap-3 text-center">
          <div class="bg-gray-50 rounded-lg p-3">
            <div class="text-lg font-bold text-blue-600">85%</div>
            <div class="text-xs text-gray-500">使用率</div>
          </div>
          <div class="bg-gray-50 rounded-lg p-3">
            <div class="text-lg font-bold text-green-600">1250</div>
            <div class="text-xs text-gray-500">总库存</div>
          </div>
          <div class="bg-gray-50 rounded-lg p-3">
            <div class="text-lg font-bold text-amber-600">32</div>
            <div class="text-xs text-gray-500">待入库</div>
          </div>
          <div class="bg-gray-50 rounded-lg p-3">
            <div class="text-lg font-bold text-red-600">48</div>
            <div class="text-xs text-gray-500">待出库</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="bg-gray-50 min-h-screen px-4 py-4 rounded-t-3xl -mt-4 relative z-10">
      <!-- 监控摄像头区域 -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
          <div class="text-base font-bold text-gray-800">监控摄像头</div>
          <div class="text-xs text-blue-500">查看全部</div>
        </div>

        <div class="grid grid-cols-2 gap-3">
          <div v-for="(camera, index) in cameraList" :key="index" class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100">
            <div class="relative">
              <div class="h-32 w-full bg-gray-200">
                <img :src="camera.image" alt="监控画面" class="w-full h-full object-cover" />
                <div class="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full flex items-center">
                  <div class="w-1.5 h-1.5 bg-red-500 rounded-full mr-1 animate-pulse"></div>
                  <span>实时</span>
                </div>
              </div>
            </div>
            <div class="p-3">
              <div class="flex justify-between items-center">
                <div class="font-medium text-sm text-gray-800">{{ camera.name }}</div>
                <div class="text-xs px-2 py-0.5 rounded-full" :class="camera.statusClass">
                  {{ camera.status }}
                </div>
              </div>
              <div class="text-xs text-gray-500 mt-1">更新于 {{ camera.lastUpdate }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 温湿度监控区域 -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <div class="h-6 w-6 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg flex items-center justify-center mr-2">
              <div class="text-xs text-white">🌡️</div>
            </div>
            <div class="text-base font-bold text-gray-800">温湿度监控</div>
          </div>
          <div class="flex items-center text-xs text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full">
            <div class="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-1 animate-pulse"></div>
            <span>实时数据</span>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-3">
          <div v-for="(sensor, index) in sensorList" :key="index" class="p-4 bg-gradient-to-br from-gray-50 to-cyan-50 rounded-lg border border-gray-100">
            <div class="flex items-center justify-between mb-2">
              <div class="h-9 w-9 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center text-white text-sm">
                {{ sensor.icon }}
              </div>
              <div class="text-xs" :class="sensor.statusColor">
                {{ sensor.status }}
              </div>
            </div>
            <div class="font-medium text-gray-800">
              {{ sensor.name }}
            </div>
            <div class="text-2xl font-bold mt-2" :class="sensor.valueColor">{{ sensor.value }}</div>
            <div class="flex items-center mt-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400 mr-1">
                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>
                <path d="M12 7v5l3 3"></path>
              </svg>
              <div class="text-xs text-gray-500">{{ sensor.lastUpdate }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 货物信息区域 -->
      <div>
        <div class="flex items-center justify-between mb-4">
          <div class="text-base font-bold text-gray-800">货物信息</div>
          <div class="flex space-x-2">
            <div class="bg-blue-50 text-blue-600 px-3 py-1 rounded-md text-xs font-medium">
              全部
            </div>
            <div class="bg-gray-100 text-gray-600 px-3 py-1 rounded-md text-xs font-medium">
              已入库
            </div>
            <div class="bg-gray-100 text-gray-600 px-3 py-1 rounded-md text-xs font-medium">
              待出库
            </div>
          </div>
        </div>

        <div class="space-y-3">
          <div v-for="(goods, index) in goodsList" :key="index" class="bg-white rounded-lg shadow-sm overflow-hidden flex border border-gray-100">
            <div class="relative h-24 w-24 flex-shrink-0">
              <img :src="goods.image" :alt="goods.name" class="w-full h-full object-cover" />
              <div class="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs py-1 px-2 text-center">
                {{ goods.quantity }} {{ goods.unit }}
              </div>
            </div>
            <div class="flex-1 p-3 flex flex-col">
              <div class="flex items-center justify-between">
                <div class="font-medium text-gray-800">{{ goods.name }}</div>
                <div class="text-xs px-2 py-0.5 rounded-full" :class="goods.statusClass">
                  {{ goods.status }}
                </div>
              </div>
              <div class="text-xs text-gray-500 mt-1">{{ goods.location }}</div>
              <div class="text-xs text-gray-500">批次号: {{ goods.batchNo }}</div>
              <div class="mt-auto flex justify-between items-center">
                <div class="text-xs text-gray-500">{{ goods.updateTime }}</div>
                <div class="text-xs text-blue-500">查看详情</div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-center mt-4 mb-20">
          <div class="text-sm text-gray-500">- 显示全部 -</div>
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
import { reactive } from 'vue';

// 模拟监控摄像头数据
const cameraList = reactive([
  {
    name: '入库区摄像头A',
    status: '正常',
    statusClass: 'bg-green-100 text-green-600',
    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
    lastUpdate: '1分钟前'
  },
  {
    name: '仓储区摄像头B',
    status: '正常',
    statusClass: 'bg-green-100 text-green-600',
    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
    lastUpdate: '2分钟前'
  },
  {
    name: '出库区摄像头C',
    status: '离线',
    statusClass: 'bg-gray-100 text-gray-600',
    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
    lastUpdate: '1小时前'
  },
  {
    name: '周边区域摄像头D',
    status: '正常',
    statusClass: 'bg-green-100 text-green-600',
    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
    lastUpdate: '1分钟前'
  }
]);

// 模拟温湿度传感器数据
const sensorList = reactive([
  {
    name: 'A区温度',
    value: '18.5°C',
    icon: '🌡️',
    status: '正常',
    statusColor: 'text-green-600',
    valueColor: 'text-blue-600',
    lastUpdate: '更新于 3分钟前'
  },
  {
    name: 'A区湿度',
    value: '65%',
    icon: '💧',
    status: '正常',
    statusColor: 'text-green-600',
    valueColor: 'text-cyan-600',
    lastUpdate: '更新于 3分钟前'
  },
  {
    name: 'B区温度',
    value: '22.1°C',
    icon: '🌡️',
    status: '偏高',
    statusColor: 'text-amber-600',
    valueColor: 'text-amber-600',
    lastUpdate: '更新于 1分钟前'
  },
  {
    name: 'B区湿度',
    value: '45%',
    icon: '💧',
    status: '正常',
    statusColor: 'text-green-600',
    valueColor: 'text-cyan-600',
    lastUpdate: '更新于 1分钟前'
  }
]);

// 模拟货物数据
const goodsList = reactive([
  {
    name: '优质花生米 精选大粒',
    quantity: '1200',
    unit: 'kg',
    status: '已入库',
    statusClass: 'bg-green-100 text-green-600',
    location: 'A区-01-03货架',
    batchNo: 'BH20230512001',
    updateTime: '2023-05-12 入库',
    image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'
  },
  {
    name: '东北大米 五常稻花香',
    quantity: '850',
    unit: 'kg',
    status: '待出库',
    statusClass: 'bg-blue-100 text-blue-600',
    location: 'B区-02-05货架',
    batchNo: 'BH20230510002',
    updateTime: '2023-05-10 入库',
    image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'
  },
  {
    name: '新疆棉花 长绒棉',
    quantity: '500',
    unit: 'kg',
    status: '已入库',
    statusClass: 'bg-green-100 text-green-600',
    location: 'C区-03-01货架',
    batchNo: 'BH20230508003',
    updateTime: '2023-05-08 入库',
    image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'
  }
]);
</script>

<style lang="scss" scoped></style>

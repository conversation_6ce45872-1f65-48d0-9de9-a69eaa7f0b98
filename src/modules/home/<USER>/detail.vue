<template>
  <ly-layout>
    <div class="pb-4">
      <!-- 药材图片轮播 -->
      <div class="relative w-full h-72 bg-gradient-to-br from-emerald-100 to-teal-200 overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-teal-500/30"></div>
        <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
        
        <!-- 药材图片 -->
        <img 
          src="https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500" 
          alt="药材图片" 
          class="w-full h-full object-cover relative z-10"
        />
        
        <!-- 渐变遮罩 -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent z-20"></div>
      </div>

      <!-- 基本信息 -->
      <div class="bg-white p-4 -mt-6 rounded-t-2xl shadow-sm relative z-40">
        <!-- 药材名称和规格 -->
        <div class="flex items-start justify-between mb-4">
          <div>
            <div class="text-xl font-bold text-gray-800 mb-1">{{ medicine.name }}</div>
            <div class="text-sm text-gray-500 flex items-center">
              <i class="i-mdi-tag-outline mr-1"></i>
              {{ medicine.spec }}
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <div class="px-2 py-1 bg-emerald-100 rounded-full">
              <span class="text-xs font-medium text-emerald-700">{{ medicine.category }}</span>
            </div>
          </div>
        </div>

        <!-- 今日市价 -->
        <div class="relative p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg overflow-hidden">
          <!-- 背景装饰 -->
          <div class="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
          <div class="absolute bottom-0 left-0 w-16 h-16 bg-white/10 rounded-full translate-y-8 -translate-x-8"></div>
          
          <div class="relative z-10">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center">
                <i class="i-mdi-currency-cny text-white/80 mr-2 text-lg"></i>
                <span class="text-sm text-white/90 font-medium">今日市价</span>
              </div>
              <div class="text-xs text-white/70 bg-white/20 px-2 py-1 rounded-full">
                {{ getCurrentDate() }}
              </div>
            </div>
            <div class="flex items-end justify-between">
              <div class="flex items-end">
                <div class="text-3xl font-bold text-white mr-2">¥{{ medicine.price }}</div>
                <div class="text-sm text-white/80 mb-1">/kg</div>
              </div>
              <div class="text-right">
                <div class="text-xs text-white/70">实时更新</div>
                <div class="flex items-center mt-1">
                  <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-1"></div>
                  <span class="text-xs text-white/80">在线</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 价格对比 -->
        <div class="mt-5 grid grid-cols-3 gap-3">
          <div class="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 text-center border border-gray-200 hover:shadow-md transition-all duration-200">
            <div class="absolute top-2 right-2">
              <i class="i-mdi-calendar-week text-gray-400 text-xs"></i>
            </div>
            <div class="text-xs text-gray-500 mb-2 font-medium">较上周</div>
            <div
              class="text-sm font-bold flex items-center justify-center"
              :class="{
                'text-green-600': isPositive(medicine.priceComparison.lastWeek),
                'text-red-600': isNegative(medicine.priceComparison.lastWeek),
                'text-gray-500': isZero(medicine.priceComparison.lastWeek),
              }"
            >
              <i 
                :class="{
                  'i-mdi-trending-up': isPositive(medicine.priceComparison.lastWeek),
                  'i-mdi-trending-down': isNegative(medicine.priceComparison.lastWeek),
                  'i-mdi-trending-neutral': isZero(medicine.priceComparison.lastWeek),
                }"
                class="mr-1 text-xs"
              ></i>
              {{ medicine.priceComparison.lastWeek }}
            </div>
          </div>
          <div class="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 text-center border border-gray-200 hover:shadow-md transition-all duration-200">
            <div class="absolute top-2 right-2">
              <i class="i-mdi-calendar-month text-gray-400 text-xs"></i>
            </div>
            <div class="text-xs text-gray-500 mb-2 font-medium">较上月</div>
            <div
              class="text-sm font-bold flex items-center justify-center"
              :class="{
                'text-green-600': isPositive(medicine.priceComparison.lastMonth),
                'text-red-600': isNegative(medicine.priceComparison.lastMonth),
                'text-gray-500': isZero(medicine.priceComparison.lastMonth),
              }"
            >
              <i 
                :class="{
                  'i-mdi-trending-up': isPositive(medicine.priceComparison.lastMonth),
                  'i-mdi-trending-down': isNegative(medicine.priceComparison.lastMonth),
                  'i-mdi-trending-neutral': isZero(medicine.priceComparison.lastMonth),
                }"
                class="mr-1 text-xs"
              ></i>
              {{ medicine.priceComparison.lastMonth }}
            </div>
          </div>
          <div class="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 text-center border border-gray-200 hover:shadow-md transition-all duration-200">
            <div class="absolute top-2 right-2">
              <i class="i-mdi-calendar text-gray-400 text-xs"></i>
            </div>
            <div class="text-xs text-gray-500 mb-2 font-medium">较去年</div>
            <div
              class="text-sm font-bold flex items-center justify-center"
              :class="{
                'text-green-600': isPositive(medicine.priceComparison.lastYear),
                'text-red-600': isNegative(medicine.priceComparison.lastYear),
                'text-gray-500': isZero(medicine.priceComparison.lastYear),
              }"
            >
              <i 
                :class="{
                  'i-mdi-trending-up': isPositive(medicine.priceComparison.lastYear),
                  'i-mdi-trending-down': isNegative(medicine.priceComparison.lastYear),
                  'i-mdi-trending-neutral': isZero(medicine.priceComparison.lastYear),
                }"
                class="mr-1 text-xs"
              ></i>
              {{ medicine.priceComparison.lastYear }}
            </div>
          </div>
        </div>

        <!-- 产地和库存信息 -->
        <div class="mt-5 grid grid-cols-2 gap-3">
          <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-3 border border-emerald-100">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                <i class="i-mdi-map-marker text-emerald-600 text-sm"></i>
              </div>
              <div>
                <div class="text-xs text-gray-500 mb-1">产地</div>
                <div class="text-sm font-medium text-gray-800">{{ medicine.origin }}</div>
              </div>
            </div>
          </div>
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-3 border border-blue-100">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <i class="i-mdi-package-variant text-blue-600 text-sm"></i>
              </div>
              <div>
                <div class="text-xs text-gray-500 mb-1">库存</div>
                <div class="text-sm font-medium text-gray-800">{{ medicine.inventory }}kg</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 价格趋势 -->
      <div class="bg-white mt-4 p-4  shadow-sm">
        <div class="flex items-center justify-between mb-6">
          <div class="text-lg font-bold flex items-center">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-3">
              <i class="i-mdi-chart-line text-white text-lg"></i>
            </div>
            <div>
              <div class="text-gray-800">历史价格走势</div>
              <div class="text-xs text-gray-500 mt-1">实时数据分析</div>
            </div>
          </div>
          <div class="flex bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl p-1 shadow-inner">
            <div
              v-for="(period, index) in periods"
              :key="index"
              :class="{
                'bg-white text-blue-600 shadow-md': currentPeriod === index,
                'text-gray-600 hover:text-gray-800': currentPeriod !== index,
              }"
              class="text-xs px-3 py-2 cursor-pointer rounded-lg transition-all duration-200 font-medium"
              @click="changePeriod(index)"
            >
              {{ period }}
            </div>
          </div>
        </div>

        <!-- 价格统计信息 -->
        <div class="grid grid-cols-4 gap-3 mb-6">
          <div class="bg-gradient-to-br from-red-50 to-pink-50 rounded-xl p-3 text-center border border-red-100">
            <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <i class="i-mdi-trending-up text-red-600 text-xs"></i>
            </div>
            <div class="text-xs text-gray-500 mb-1">最高价</div>
            <div class="text-sm font-bold text-red-600">¥{{ priceStats.highest }}</div>
          </div>
          <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-3 text-center border border-green-100">
            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <i class="i-mdi-trending-down text-green-600 text-xs"></i>
            </div>
            <div class="text-xs text-gray-500 mb-1">最低价</div>
            <div class="text-sm font-bold text-green-600">¥{{ priceStats.lowest }}</div>
          </div>
          <div class="bg-gradient-to-br from-gray-50 to-slate-50 rounded-xl p-3 text-center border border-gray-100">
            <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <i class="i-mdi-calculator text-gray-600 text-xs"></i>
            </div>
            <div class="text-xs text-gray-500 mb-1">平均价</div>
            <div class="text-sm font-bold text-gray-700">¥{{ priceStats.average }}</div>
          </div>
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-3 text-center border border-blue-100">
            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <i class="i-mdi-pulse text-blue-600 text-xs"></i>
            </div>
            <div class="text-xs text-gray-500 mb-1">波动率</div>
            <div class="text-sm font-bold text-blue-600">{{ priceStats.volatility }}%</div>
          </div>
        </div>

        <!-- 图表容器 -->
        <div class="relative w-full h-72 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-2xl border border-blue-100 shadow-inner overflow-hidden">
          <!-- 背景装饰 -->
          <div class="absolute top-0 right-0 w-24 h-24 bg-white/20 rounded-full -translate-y-12 translate-x-12"></div>
          <div class="absolute bottom-0 left-0 w-20 h-20 bg-white/20 rounded-full translate-y-10 -translate-x-10"></div>
          
          <qiun-data-charts 
            type="line" 
            :opts="chartOpts" 
            :chartData="chartData" 
            :canvas2d="true"
            :canvasId="canvasId"
            :canvas-id="canvasId"
            @getIndex="getIndex"
            class="w-full h-full relative z-10" 
          />
          
          <!-- 图表加载状态 -->
          <div v-if="chartLoading" class="absolute inset-0 flex items-center justify-center bg-white/90 backdrop-blur-sm z-20">
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 border-3 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-3"></div>
              <span class="text-sm text-gray-600">数据加载中...</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 品质与规格 -->
      <div class="bg-white mt-4  p-4  shadow-sm">
        <div class="text-lg font-bold mb-5 flex items-center">
          <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-3">
            <i class="i-mdi-clipboard-list text-white text-lg"></i>
          </div>
          <div>
            <div class="text-gray-800">品质与规格</div>
            <div class="text-xs text-gray-500 mt-1">详细规格参数</div>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div v-for="(detail, index) in medicine.details" :key="index" 
               class="bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-100 rounded-xl p-4 hover:shadow-md transition-all duration-200 group">
            <div class="flex items-center justify-between mb-2">
              <div class="text-xs text-emerald-600 font-medium">{{ detail.title }}</div>
              <div class="w-4 h-4 bg-emerald-100 rounded-full flex items-center justify-center group-hover:bg-emerald-200 transition-colors duration-200">
                <i class="i-mdi-check text-emerald-600 text-xs"></i>
              </div>
            </div>
            <div class="text-sm font-bold text-gray-800">{{ detail.content }}</div>
          </div>
        </div>
      </div>

      <!-- 药材介绍 -->
      <div class="bg-white mt-4 p-4  shadow-sm">
        <div class="text-lg font-bold mb-5 flex items-center">
          <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center mr-3">
            <i class="i-mdi-book-open-page-variant text-white text-lg"></i>
          </div>
          <div>
            <div class="text-gray-800">药材介绍</div>
            <div class="text-xs text-gray-500 mt-1">功效与用途说明</div>
          </div>
        </div>
        <div class="bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl p-4 border border-amber-100 shadow-inner">
          <div class="flex items-start mb-4">
            <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
              <i class="i-mdi-leaf text-amber-600 text-xl"></i>
            </div>
            <div>
              <div class="text-base font-bold text-amber-700 mb-2">中药功效</div>
              <div class="text-sm text-gray-700 leading-relaxed">
                {{ medicine.description }}
              </div>
            </div>
          </div>
          
          <!-- 功效标签 -->
          <div class="flex flex-wrap gap-2 mt-4">
            <div class="bg-white/80 px-3 py-1 rounded-full">
              <span class="text-xs font-medium text-amber-700">活血行气</span>
            </div>
            <div class="bg-white/80 px-3 py-1 rounded-full">
              <span class="text-xs font-medium text-amber-700">祛风止痛</span>
            </div>
            <div class="bg-white/80 px-3 py-1 rounded-full">
              <span class="text-xs font-medium text-amber-700">传统中药</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 质量标准 -->
      <div class="bg-white mt-4 p-4  shadow-sm">
        <div class="text-lg font-bold mb-5 flex items-center">
          <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-3">
            <i class="i-mdi-shield-check text-white text-lg"></i>
          </div>
          <div>
            <div class="text-gray-800">质量标准</div>
            <div class="text-xs text-gray-500 mt-1">权威认证保障</div>
          </div>
        </div>
        
        <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-4 border border-green-100 shadow-inner">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
              <i class="i-mdi-certificate text-green-600 text-xl"></i>
            </div>
            <div>
              <div class="text-base font-bold text-green-700">国家中药材质量标准</div>
              <div class="text-xs text-green-600 mt-1">National Quality Standards</div>
            </div>
          </div>
          
          <div class="bg-white/70 rounded-xl p-4 mb-4">
            <div class="text-sm text-gray-700 leading-relaxed">
              本药材符合《中国药典》({{ medicine.pharmacopoeia }})标准要求，经过严格的质量检测和认证流程。
            </div>
          </div>
          
          <div class="grid grid-cols-3 gap-3">
            <div class="bg-white/80 rounded-xl p-3 text-center">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <i class="i-mdi-check-decagram text-green-600 text-sm"></i>
              </div>
              <div class="text-xs font-medium text-green-700">质检合格</div>
              <div class="text-xs text-gray-600 mt-1">Quality Tested</div>
            </div>
            <div class="bg-white/80 rounded-xl p-3 text-center">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <i class="i-mdi-qrcode-scan text-green-600 text-sm"></i>
              </div>
              <div class="text-xs font-medium text-green-700">溯源可查</div>
              <div class="text-xs text-gray-600 mt-1">Traceable</div>
            </div>
            <div class="bg-white/80 rounded-xl p-3 text-center">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <i class="i-mdi-leaf text-green-600 text-sm"></i>
              </div>
              <div class="text-xs font-medium text-green-700">GAP认证</div>
              <div class="text-xs text-gray-600 mt-1">GAP Certified</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 相关药材推荐 -->
      <div class="bg-white mt-4 p-4 r shadow-sm">
        <div class="text-lg font-bold mb-5 flex items-center">
          <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mr-3">
            <i class="i-mdi-lightbulb text-white text-lg"></i>
          </div>
          <div>
            <div class="text-gray-800">相关药材推荐</div>
            <div class="text-xs text-gray-500 mt-1">精选优质药材</div>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div v-for="(item, index) in relatedMedicines" :key="index" 
               class="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-100 rounded-2xl p-4 hover:shadow-lg transition-all duration-300 cursor-pointer group">
            <div class="flex items-start justify-between mb-3">
              <div class="text-sm font-bold text-gray-800 group-hover:text-purple-700 transition-colors duration-200">{{ item.name }}</div>
              <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-200">
                <i class="i-mdi-arrow-right text-purple-600 text-xs"></i>
              </div>
            </div>
            <div class="text-xs text-gray-500 mb-3 flex items-center">
              <i class="i-mdi-tag-outline mr-1"></i>
              {{ item.spec }}
            </div>
            <div class="flex items-center justify-between">
              <div class="text-base font-bold text-purple-600">¥{{ item.price }}</div>
              <div class="text-xs text-purple-500 bg-purple-100 px-2 py-1 rounded-full">查看详情</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部固定栏 -->
      <div v-if="false" class="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-lg border-t border-gray-100 flex items-center px-5 py-4 shadow-2xl z-50">
        <div class="flex-1 flex items-center justify-around">
          <div class="flex flex-col items-center cursor-pointer group">
            <div class="w-10 h-10 rounded-xl bg-gray-100 flex items-center justify-center group-hover:bg-blue-100 transition-all duration-200 group-active:scale-95">
              <i class="i-mdi-home-outline text-gray-600 group-hover:text-blue-600 text-lg transition-colors duration-200"></i>
            </div>
            <div class="text-xs text-gray-500 group-hover:text-blue-600 mt-1 transition-colors duration-200">首页</div>
          </div>
          <div class="flex flex-col items-center cursor-pointer group">
            <div class="w-10 h-10 rounded-xl bg-gray-100 flex items-center justify-center group-hover:bg-orange-100 transition-all duration-200 group-active:scale-95 relative">
              <i class="i-mdi-cart-outline text-gray-600 group-hover:text-orange-600 text-lg transition-colors duration-200"></i>
              <!-- 购物车数量徽章 -->
              <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <span class="text-xs text-white font-bold">2</span>
              </div>
            </div>
            <div class="text-xs text-gray-500 group-hover:text-orange-600 mt-1 transition-colors duration-200">购物车</div>
          </div>
          <div class="flex flex-col items-center cursor-pointer group" @click="toggleFavorite">
            <div class="w-10 h-10 rounded-xl bg-gray-100 flex items-center justify-center group-hover:bg-pink-100 transition-all duration-200 group-active:scale-95">
              <i :class="isFavorite ? 'i-mdi-heart text-pink-600' : 'i-mdi-heart-outline text-gray-600 group-hover:text-pink-600'" class="text-lg transition-colors duration-200"></i>
            </div>
            <div class="text-xs text-gray-500 group-hover:text-pink-600 mt-1 transition-colors duration-200">{{ isFavorite ? '已收藏' : '收藏' }}</div>
          </div>
        </div>
        <div class="flex-1 ml-5 flex space-x-3">
          <button class="flex-1 bg-gradient-to-r from-emerald-500 to-teal-600 text-white py-3 rounded-2xl text-sm font-bold shadow-lg hover:shadow-xl transition-all duration-200 active:scale-95 flex items-center justify-center">
            <i class="i-mdi-phone mr-2"></i>
            联系供应商
          </button>
          <button class="flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 rounded-2xl text-sm font-bold shadow-lg hover:shadow-xl transition-all duration-200 active:scale-95 flex items-center justify-center">
            <i class="i-mdi-cart-plus mr-2"></i>
            立即采购
          </button>
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  // 时间周期选项
  const periods = ['1周', '1月', '3月', '1年', '全部'];
  const currentPeriod = ref(1);

  // 图表相关
  const canvasId = ref('priceChart');
  const chartLoading = ref(false);
  const chartData = ref({});
  const chartOpts = ref({});

  // 收藏状态
  const isFavorite = ref(false);

  // 药材详情数据
  const medicine = ref({
    id: '',
    name: '',
    spec: '',
    origin: '',
    price: 0,
    change: '',
    inventory: 0,
    category: '',
    pharmacopoeia: '',
    description: '',
    details: [],
    priceComparison: {
      lastWeek: '',
      lastMonth: '',
      lastYear: '',
    },
    historicalPrices: [],
  });

  // 相关药材推荐
  const relatedMedicines = [
    {
      id: '5',
      name: '三七',
      spec: '20头',
      price: 435.0,
    },
    {
      id: '6',
      name: '丹参',
      spec: '特级',
      price: 28.5,
    },
    {
      id: '11',
      name: '当归',
      spec: '统货',
      price: 65.0,
    },
    {
      id: '12',
      name: '赤芍',
      spec: '特级',
      price: 42.5,
    },
  ];

  // 价格统计计算
  const priceStats = computed(() => {
    const prices = medicine.value.historicalPrices;
    if (!prices || prices.length === 0) {
      return { highest: 0, lowest: 0, average: 0, volatility: 0 };
    }

    const values = prices.map((p) => p.price);
    const highest = Math.max(...values);
    const lowest = Math.min(...values);
    const average = (values.reduce((a, b) => a + b, 0) / values.length).toFixed(1);
    const volatility = (((highest - lowest) / average) * 100).toFixed(1);

    return { highest, lowest, average, volatility };
  });

  // 获取当前日期
  const getCurrentDate = () => {
    const now = new Date();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    return `${month}月${day}日`;
  };

  // 判断涨跌幅是否为正数
  const isPositive = (change: string) => change.startsWith('+') && change !== '+0.0%';

  // 判断涨跌幅是否为负数
  const isNegative = (change: string) => change.startsWith('-');

  // 判断涨跌幅是否为零
  const isZero = (change: string) => change === '0.0%' || change === '+0.0%';

  // 返回上一页
  const goBack = () => {
    monkey.$router.navigateBack();
  };

  // 切换收藏状态
  const toggleFavorite = () => {
    isFavorite.value = !isFavorite.value;
    // 这里可以添加收藏到本地存储或发送到服务器的逻辑
    if (isFavorite.value) {
      // 添加收藏成功提示
      console.log('已添加到收藏');
    } else {
      // 取消收藏成功提示
      console.log('已取消收藏');
    }
  };

  // 切换时间周期
  const changePeriod = (index: number) => {
    currentPeriod.value = index;
    updateChartData();
  };

  // 图表点击事件
  const getIndex = (e: any) => {
    console.log('图表点击事件:', e);
  };

  // 初始化图表配置
  const initChartOpts = () => {
    chartOpts.value = {
      color: ['#3B82F6', '#10B981', '#F59E0B'],
      padding: [15, 15, 0, 15],
      enableScroll: false,
      legend: {
        show: false,
      },
      xAxis: {
        disableGrid: true,
        itemCount: 7,
        scrollShow: true,
        labelCount: 7,
      },
      yAxis: {
        gridType: 'dash',
        dashLength: 2,
        data: [
          {
            calibration: true,
            position: 'left',
            title: '价格(元/kg)',
            titleFontSize: 12,
            titleOffsetX: -10,
          },
        ],
      },
      extra: {
        line: {
          type: 'curve',
          width: 2,
          activeType: 'hollow',
        },
      },
    };
  };

  // 更新图表数据
  const updateChartData = () => {
    chartLoading.value = true;

    setTimeout(() => {
      const prices = getHistoricalPricesByPeriod(currentPeriod.value);

      chartData.value = {
        categories: prices.map((p) => p.date),
        series: [
          {
            name: '价格',
            data: prices.map((p) => p.price),
          },
        ],
      };

      chartLoading.value = false;
    }, 500);
  };

  // 根据时间周期获取历史价格数据
  const getHistoricalPricesByPeriod = (periodIndex: number) => {
    const allPrices = medicine.value.historicalPrices;
    if (!allPrices || allPrices.length === 0) return [];

    const now = new Date();
    let startDate = new Date();

    switch (periodIndex) {
      case 0: // 1周
        startDate.setDate(now.getDate() - 7);
        break;
      case 1: // 1月
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 2: // 3月
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 3: // 1年
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      case 4: // 全部
        return allPrices;
    }

    return allPrices.filter((p) => new Date(p.date) >= startDate);
  };

  // 生成历史价格数据
  const generateHistoricalPrices = (basePrice: number, days: number = 90) => {
    const prices = [];
    const now = new Date();

    for (let i = days; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);

      // 生成价格波动（基于基础价格的±15%范围内）
      const variation = (Math.random() - 0.5) * 0.3; // ±15%
      const price = Math.round(basePrice * (1 + variation) * 10) / 10;

      prices.push({
        date: `${date.getMonth() + 1}-${date.getDate()}`,
        price: price,
        fullDate: date.toISOString().split('T')[0],
      });
    }

    return prices;
  };

  // 模拟药材数据库
  const medicineDatabase = [
    {
      id: '1',
      name: '川芎',
      spec: '统货',
      origin: '四川产区',
      price: 45.8,
      change: '+4.5%',
      inventory: 1200,
      category: '活血类',
      pharmacopoeia: '2020年版一部',
      description: '川芎为伞形科植物川芎的干燥根茎。具有活血行气，祛风止痛的功效。常用于治疗经闭、痛经、胸胁刺痛、跌打损伤、风湿痹痛、头痛、风寒湿痹等症状。川芎为常用中药材之一，广泛用于中成药和中药饮片的制作。',
      priceComparison: {
        lastWeek: '+2.3%',
        lastMonth: '+8.7%',
        lastYear: '+15.2%',
      },
      historicalPrices: generateHistoricalPrices(45.8),
      details: [
        { title: '产地', content: '四川省汉源县' },
        { title: '等级', content: '统货' },
        { title: '采收期', content: '9-10月' },
        { title: '保质期', content: '24个月' },
        { title: '储存方式', content: '通风干燥处' },
        { title: '用药部位', content: '根茎' },
      ],
    },
    {
      id: '2',
      name: '党参',
      spec: '统货',
      origin: '甘肃岷县',
      price: 62.5,
      change: '-1.2%',
      inventory: 850,
      category: '补气类',
      pharmacopoeia: '2020年版一部',
      description: '党参为桔梗科植物党参的干燥根。具有补中益气，健脾益肺的功效。常用于脾肺虚弱，气短心悸，食少倦怠，虚喘咳嗽，内热消渴等症状。党参是常用的补气中药材，也是多种中成药的重要组成部分。',
      priceComparison: {
        lastWeek: '-0.8%',
        lastMonth: '-3.2%',
        lastYear: '+12.8%',
      },
      historicalPrices: generateHistoricalPrices(62.5),
      details: [
        { title: '产地', content: '甘肃岷县' },
        { title: '等级', content: '统货' },
        { title: '采收期', content: '8-9月' },
        { title: '保质期', content: '36个月' },
        { title: '储存方式', content: '通风干燥处' },
        { title: '用药部位', content: '根' },
      ],
    },
  ];

  // 根据ID加载药材详情
  const loadMedicineDetail = (id: string) => {
    // 在实际开发中，这里应该是API请求
    const found = medicineDatabase.find((item) => item.id === id);
    if (found) {
      medicine.value = found;
    } else {
      // 如果找不到药材，可以显示一个默认药材或提示
      medicine.value = medicineDatabase[0];
    }
  };

  // 组件挂载时获取路由参数并加载数据
  onLoad((options) => {
    console.log(options);
    const medicineId = options.id as string;
    if (medicineId) {
      loadMedicineDetail(medicineId);
    } else {
      // 如果没有ID参数，加载第一个药材
      loadMedicineDetail('1');
    }
    // 初始化图表
    initChartOpts();
    setTimeout(() => {
      updateChartData();
    }, 1000);
  });
</script>

<style lang="scss" scoped>
  /* 自定义动画样式 */
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-slideInRight {
    animation: slideInRight 0.6s ease-out;
  }

  /* 图表容器样式优化 */
  .chart-container {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  }

  /* 渐变背景优化 */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  /* 悬停效果增强 */
  .hover-scale:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease-in-out;
  }

  /* 价格卡片阴影效果 */
  .price-card {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: box-shadow 0.3s ease-in-out;
  }

  .price-card:hover {
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* 毛玻璃效果 */
  .backdrop-blur-lg {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 渐变文字效果 */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 卡片悬停效果 */
  .card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* 按钮点击效果 */
  .btn-press:active {
    transform: scale(0.98);
  }

  /* 加载动画优化 */
  .loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
  }
</style>

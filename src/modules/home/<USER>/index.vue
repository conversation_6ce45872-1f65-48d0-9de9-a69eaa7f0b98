<template>
  <div class="flex flex-col min-h-screen bg-gradient-to-b from-blue-50 to-gray-50">
    <!-- 顶部搜索区域 -->
    <di v-if="false" class="bg-white/90 backdrop-blur-sm shadow-sm px-4 py-3 mb-4 sticky top-0 z-10">
      <!-- 搜索栏 -->
      <uni-search-bar
        v-model="searchKeyword"
        :radius="25"
        placeholder="搜索货运信息、起点、终点..."
        :clearButton="'auto'"
        :cancelButton="'none'"
        bgColor="#f8f9fa"
        @input="handleSearch"
        @clear="handleClear"
        @confirm="handleSearchConfirm"
        class="mb-3"
      />

      <!-- 筛选标签 -->
      <div class="flex gap-2 overflow-x-auto pb-1 scrollbar-hide">
        <div
          v-for="(filter, index) in filterTags"
          :key="index"
          :class="[
            'flex-shrink-0 px-3 py-1.5 rounded-full text-xs border transition-all duration-200 cursor-pointer',
            activeFilter === index
              ? 'bg-blue-500 text-white border-blue-500 shadow-md transform scale-105'
              : 'bg-white text-gray-600 border-gray-200 hover:border-blue-300 hover:shadow-sm hover:bg-blue-50',
          ]"
          @click="setActiveFilter(index)"
        >
          {{ filter.label }}
        </div>
      </div>
    </di>

    <!-- 当前无货运信息的提示 -->
    <div v-if="filteredFreightList.length === 0" class="flex flex-col items-center justify-center py-20">
      <uni-icons type="info" size="60" color="#CCCCCC"></uni-icons>
      <view class="text-gray-400 mt-4 text-base">{{ searchKeyword ? '未找到相关货运信息' : '暂无货运信息' }}</view>
      <view v-if="searchKeyword" class="text-gray-300 mt-2 text-sm">试试其他关键词</view>
    </div>

    <!-- 货运信息列表 -->
    <div v-else class="px-4 pb-4">
      <div class="flex flex-col gap-4">
        <div
          v-for="(item, index) in filteredFreightList"
          :key="index"
          class="bg-white rounded-xl shadow-sm p-5 border border-gray-100 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer"
          @click="viewFreightDetail(item)"
        >
          <!-- 标题和状态 -->
          <div class="flex justify-between items-start mb-4">
            <div class="flex-1 pr-3">
              <div class="font-bold text-gray-800 text-base mb-1">{{ item.title }}</div>
              <div class="text-xs text-gray-500">发布时间：{{ item.publishTime }}</div>
            </div>
            <div class="px-3 py-1 text-xs rounded-full font-medium" :class="getStatusStyle(item.status)">
              {{ item.status }}
            </div>
          </div>

          <!-- 路线信息 -->
          <div class="flex items-center mb-4">
            <div class="flex flex-col flex-1 mr-3">
              <div class="flex items-center mb-3">
                <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                  <div class="w-2.5 h-2.5 rounded-full bg-blue-500"></div>
                </div>
                <div class="text-sm text-gray-700 flex-1">
                  <div class="font-medium">{{ item.fromCity }}</div>
                  <div class="text-xs text-gray-500 mt-0.5 truncate">{{ item.fromAddress }}</div>
                </div>
              </div>

              <!-- 路线连接线 -->
              <div class="flex items-center ml-3 mb-3 relative">
                <div class="w-px h-6 bg-gradient-to-b from-blue-400 to-red-400"></div>
                <div class="flex-1 mx-2 border-t border-dashed border-gray-300 relative">
                  <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-400 rounded-full"></div>
                </div>
                <div class="text-xs text-gray-400 bg-gray-50 px-2 py-1 rounded-full">{{ item.distance }}</div>
              </div>

              <div class="flex items-center">
                <div class="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center mr-3">
                  <div class="w-2.5 h-2.5 rounded-full bg-red-500"></div>
                </div>
                <div class="text-sm text-gray-700 flex-1">
                  <div class="font-medium">{{ item.toCity }}</div>
                  <div class="text-xs text-gray-500 mt-0.5 truncate">{{ item.toAddress }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 货物信息标签 -->
          <div class="flex flex-wrap gap-2 text-xs mb-4">
            <div class="px-3 py-1.5 rounded-full bg-blue-50 text-blue-600 border border-blue-100 flex items-center gap-1 hover:bg-blue-100 transition-colors">
              <uni-icons type="box" size="12" color="#2563eb"></uni-icons>
              {{ item.cargoType }}
            </div>
            <div class="px-3 py-1.5 rounded-full bg-green-50 text-green-600 border border-green-100 flex items-center gap-1 hover:bg-green-100 transition-colors">
              <uni-icons type="gear" size="12" color="#16a34a"></uni-icons>
              {{ item.weight }}
            </div>
            <div class="px-3 py-1.5 rounded-full bg-orange-50 text-orange-600 border border-orange-100 flex items-center gap-1 hover:bg-orange-100 transition-colors">
              <uni-icons type="calendar" size="12" color="#ea580c"></uni-icons>
              {{ item.time }}
            </div>
          </div>

          <!-- 价格和操作区域 -->
          <div class="flex justify-between items-center pt-3 border-t border-gray-100">
            <div class="price-highlight">
              <div class="text-2xl font-bold text-red-500">¥{{ item.price }}</div>
              <div class="text-xs text-gray-500">运费</div>
            </div>

            <div class="flex gap-2">
              <div
                v-if="item.driverPhone"
                class="flex items-center justify-center gap-1 bg-blue-500 text-white px-4 py-2 rounded-full shadow-sm hover:bg-blue-600 transition-colors duration-200"
                @click.stop="callDriver(item.driverPhone)"
              >
                <uni-icons type="phone" size="14" color="#FFFFFF"></uni-icons>
                <text class="text-sm">联系司机</text>
              </div>
              <div v-else class="flex items-center justify-center gap-1 bg-gray-100 text-gray-400 px-4 py-2 rounded-full">
                <uni-icons type="info" size="14" color="#9ca3af"></uni-icons>
                <text class="text-sm">暂无司机</text>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import monkey from '@/monkey';

  // 搜索关键词
  const searchKeyword = ref('');

  // 当前激活的筛选器
  const activeFilter = ref(0);

  // 筛选标签
  const filterTags = ref([
    { label: '全部', value: 'all' },
    { label: '待接单', value: '待接单' },
    { label: '运输中', value: '运输中' },
    { label: '已完成', value: '已完成' },
    { label: '今日装货', value: 'today' },
    { label: '高价值', value: 'high_price' },
  ]);

  // 货运信息列表
  const freightList = ref([
    {
      id: 1,
      title: '蔬菜水果运输',
      status: '待接单',
      fromCity: '广州',
      toCity: '深圳',
      fromAddress: '广州市白云区大朗蔬菜批发市场',
      toAddress: '深圳市南山区大型商超配送中心',
      distance: '140公里',
      cargoType: '蔬菜水果',
      weight: '2吨',
      time: '今日装货',
      price: '1200',
      driverPhone: '13800138000',
      publishTime: '2小时前',
      isFavorite: false,
    },
    {
      id: 2,
      title: '家具配送',
      status: '运输中',
      fromCity: '佛山',
      toCity: '广州',
      fromAddress: '佛山市顺德区家具产业园',
      toAddress: '广州市天河区某小区',
      distance: '50公里',
      cargoType: '家具',
      weight: '1.5吨',
      time: '明日装货',
      price: '800',
      driverPhone: '13900139000',
      publishTime: '5小时前',
      isFavorite: true,
    },
    {
      id: 3,
      title: '电子产品运输',
      status: '待接单',
      fromCity: '东莞',
      toCity: '广州',
      fromAddress: '东莞市松山湖科技园',
      toAddress: '广州市黄埔区保税区',
      distance: '85公里',
      cargoType: '电子产品',
      weight: '1吨',
      time: '后天装货',
      price: '1500',
      driverPhone: '',
      publishTime: '1天前',
      isFavorite: false,
    },
    {
      id: 4,
      title: '建材运输',
      status: '待接单',
      fromCity: '广州',
      toCity: '珠海',
      fromAddress: '广州市番禺区建材市场',
      toAddress: '珠海市香洲区工地',
      distance: '120公里',
      cargoType: '建材',
      weight: '3吨',
      time: '今日装货',
      price: '2000',
      driverPhone: '13700137000',
      publishTime: '30分钟前',
      isFavorite: false,
    },
  ]);

  // 过滤后的货运列表
  const filteredFreightList = computed(() => {
    let filtered = freightList.value;

    // 按筛选标签过滤
    const currentFilter = filterTags.value[activeFilter.value];
    if (currentFilter.value !== 'all') {
      if (currentFilter.value === 'today') {
        filtered = filtered.filter((item) => item.time.includes('今日'));
      } else if (currentFilter.value === 'high_price') {
        filtered = filtered.filter((item) => parseInt(item.price) >= 1500);
      } else {
        filtered = filtered.filter((item) => item.status === currentFilter.value);
      }
    }

    // 按搜索关键词过滤
    if (searchKeyword.value.trim()) {
      const keyword = searchKeyword.value.toLowerCase().trim();
      filtered = filtered.filter(
        (item) =>
          item.title.toLowerCase().includes(keyword) ||
          item.fromCity.toLowerCase().includes(keyword) ||
          item.toCity.toLowerCase().includes(keyword) ||
          item.fromAddress.toLowerCase().includes(keyword) ||
          item.toAddress.toLowerCase().includes(keyword) ||
          item.cargoType.toLowerCase().includes(keyword),
      );
    }

    return filtered;
  });

  // 设置激活的筛选器
  const setActiveFilter = (index: number) => {
    activeFilter.value = index;
  };

  // 获取状态样式
  const getStatusStyle = (status: string) => {
    switch (status) {
      case '待接单':
        return 'bg-blue-100 text-blue-600 border border-blue-200';
      case '运输中':
        return 'bg-green-100 text-green-600 border border-green-200';
      case '已完成':
        return 'bg-gray-100 text-gray-600 border border-gray-200';
      default:
        return 'bg-gray-100 text-gray-600 border border-gray-200';
    }
  };

  // 搜索处理
  const handleSearch = (value: string) => {
    searchKeyword.value = value;
  };

  // 清空搜索
  const handleClear = () => {
    searchKeyword.value = '';
  };

  // 搜索确认
  const handleSearchConfirm = () => {
    // 可以在这里添加搜索确认的逻辑
    console.log('搜索确认:', searchKeyword.value);
  };

  // 查看货运详情
  const viewFreightDetail = (item: any) => {
    monkey.$router.navigateTo(`/modules/home/<USER>/detail?id=${item.id}`);
  };

  // 切换收藏状态
  const toggleFavorite = (item: any) => {
    item.isFavorite = !item.isFavorite;
    uni.showToast({
      title: item.isFavorite ? '已收藏' : '已取消收藏',
      icon: 'none',
    });
  };

  // 拨打司机电话
  const callDriver = (phoneNumber: string) => {
    uni.makePhoneCall({
      phoneNumber,
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败', err);
        uni.showToast({
          title: '拨打电话失败',
          icon: 'none',
        });
      },
    });
  };
</script>

<style scoped lang="scss"></style>

<template>
  <ly-layout is-arc-bg>
    <!-- <uni-nav-bar :color="navBarTitleColor" :background-color="navBarBgColor" left-text="交易大厅" leftWidth="200rpx" :fixed="true" left-icon="left" :border="false" status-bar /> -->
    <div class="px-4 relative z-10 mt-4">
      <!-- 顶部蓝色卡片 -->
      <div class="mb-4 bg-blue-500 flex flex-col rounded-lg">
        <!-- 链接 -->
        <div class="flex justify-between items-center rounded-lg py-3 px-4 bg-blue-500">
          <div class="flex items-center text-white">
            <text class="font-medium">实施更新数据</text>
          </div>
          <div class="text-sm text-white">2025-07-03 10:00:00</div>
        </div>

        <!-- 余额卡片 -->
        <div class="bg-white rounded-2xl p-5 text-black shadow-lg mx-1 border border-gray-100">
          <!-- 交易指标统计 -->
          <div class="mb-4">
            <div class="flex justify-center items-center">
              <div class="text-gray-600 font-medium">累计总金额<text class="text-xs ml-1">(万元)</text></div>
            </div>
            <div class="text-3xl font-bold text-center mt-3 mb-3 text-blue-600">* * * *</div>
            <div class="h-px bg-gray-100 my-3"></div>
            <div class="flex justify-center items-center">
              <div class="text-gray-600 font-medium">累计交易量<text class="text-xs ml-1">(吨)</text></div>
            </div>
            <div class="text-3xl font-bold text-center mt-3 mb-3 text-blue-600">* * * *</div>
          </div>

          <!-- 收益信息 -->
          <div class="h-px bg-gray-100 mb-4"></div>
          <div class="grid grid-cols-4 gap-2">
            <div class="text-center">
              <div class="text-blue-600 text-lg font-bold">* * *</div>
              <div class="text-xs text-gray-500 mt-1">用户数量<text class="opacity-70">(人)</text></div>
            </div>
            <div class="text-center">
              <div class="text-blue-600 text-lg font-bold">* * *</div>
              <div class="text-xs text-gray-500 mt-1">昨日收益<text class="opacity-70">(元)</text></div>
            </div>
            <div class="text-center">
              <div class="text-blue-600 text-lg font-bold">* * *</div>
              <div class="text-xs text-gray-500 mt-1">累计收益<text class="opacity-70">(元)</text></div>
            </div>
            <div class="text-center">
              <div class="text-blue-600 text-lg font-bold">* * *</div>
              <div class="text-xs text-gray-500 mt-1">基地资源<text class="opacity-70">(亩)</text></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计数字卡片 -->
      <!-- <div class="px-5 -mt-10">
        <div class="bg-white rounded-lg shadow grid grid-cols-4 divide-x divide-gray-100">
          <div class="p-3 text-center">
            <div class="text-blue-600 font-bold">11224</div>
            <div class="text-xs text-gray-500 mt-1">订单数</div>
          </div>
          <div class="p-3 text-center">
            <div class="text-green-600 font-bold">122</div>
            <div class="text-xs text-gray-500 mt-1">品种数</div>
          </div>
          <div class="p-3 text-center">
            <div class="text-purple-600 font-bold">4817</div>
            <div class="text-xs text-gray-500 mt-1">用户数</div>
          </div>
          <div class="p-3 text-center">
            <div class="text-orange-600 font-bold">71561</div>
            <div class="text-xs text-gray-500 mt-1">资源亩</div>
          </div>
        </div>
      </div> -->

      <!-- 最新动态 -->
      <div class="mt-6">
        <div class="flex justify-between items-center mb-3">
          <div class="font-medium">最新动态</div>
        </div>

        <div class="space-y-3">
          <div v-for="(item, index) in tradeList" :key="index" class="bg-white rounded-lg p-3 relative shadow-sm">
            <div class="absolute left-0 top-0 bottom-0 w-1 rounded-l-lg" :style="{ backgroundColor: getTradeBorderColor(index) }"></div>
            <div class="pl-2">
              <div class="font-medium">{{ item.name }} ({{ item.spec }})</div>
              <div class="flex justify-between mt-1 text-xs text-gray-500">
                <div>{{ item.buyer || '采购商:' + item.name.charAt(0) + '****' }}</div>
                <div>{{ item.date }}</div>
              </div>
              <div class="text-blue-600 font-medium text-right mt-1">{{ item.amount }}万</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  const navBarBgColor = ref('transparent');
  const navBarTitleColor = ref('#fff');
  const tradeList = ref([
    { name: '三七', spec: '本店剪口', buyer: '采购商: 用******', amount: '64.20', date: '06-29 14:33' },
    { name: '桉叶油', spec: '毛油', buyer: '供应商: ********公司', amount: '10.48', date: '06-26 11:09' },
    { name: '木香', spec: '统货', buyer: '采购商: 用******', amount: '0.70', date: '06-28 10:54' },
    { name: '白及', spec: '统货个子', buyer: '采购商: 用******', amount: '0.01', date: '06-30 09:15' },
    { name: '天麻', spec: '特级', buyer: '采购商: 用******', amount: '15.30', date: '06-25 16:42' },
  ]);

  const borderColors = ['#4169E1', '#50C878', '#FF7F50', '#9370DB', '#FF6347'];
  const getTradeBorderColor = (index: number) => {
    return borderColors[index % borderColors.length];
  };

  onPageScroll((e) => {
    navBarBgColor.value = monkey.$helper.$utils.getNavBarBgColor(e.scrollTop);
    navBarTitleColor.value = monkey.$helper.$utils.getNavTitleColor(e.scrollTop);
  });
</script>

<style lang="scss" scoped>
  :deep(.uni-navbar-btn-text text) {
    font-size: 32rpx !important;
    font-weight: bolder !important;
    margin-left: 12rpx !important;
  }
</style>

<template>
  <ly-layout class="flex flex-col h-full">
    <!-- 头部固定区域 -->
    <div class="flex-shrink-0">
      <!-- Tab 切换 -->
      <div class="bg-white z-10 border-b py-1.5 border-gray-100 shadow-sm">
        <uni-segmented-control :current="tabIndex" activeColor="#2563eb" styleType="text" :values="['平台指导价', '商品成交价']" class="font-medium" @clickItem="onTabChange"></uni-segmented-control>
      </div>

      <!-- 分类选择器 -->
      <div class="p-3 bg-white mb-4 relative pr-20">
        <div class="overflow-x-auto hide-scrollbar">
          <div class="flex space-x-2.5">
            <div v-for="(category, index) in categories" :key="'cat-' + index" class="px-3 py-1.5 rounded-full text-sm whitespace-nowrap cursor-pointer transition-all flex-shrink-0" :class="currentCategory === index ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'" @click="selectCategory(index)">
              {{ category }}
            </div>
          </div>
          <!-- 更多分类 -->
          <div class="absolute right-0 top-1/2 flex items-center text-gray-600 -translate-y-1/2 px-2.5 py-1.5 rounded-l-full bg-gray-100 text-sm transition-all">
            更多
            <i class="i-mdi-dots-vertical text-xs"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间可滚动区域 -->
    <div class="flex-1 overflow-y-auto p-3 pt-0">
      <!-- 价格行情列表 -->
      <div>
        <div class="bg-white rounded-lg overflow-hidden border border-gray-100 shadow-sm">
          <!-- 表头 -->
          <div class="bg-gradient-to-r from-blue-50 to-white py-3 px-4 text-xs text-gray-800 font-medium border-b border-blue-100 flex sticky top-0 z-10">
            <div class="flex-1 text-center">商品名称</div>
            <div class="flex-1 text-center">市场</div>
            <div class="flex-1 text-center">价格</div>
            <div class="flex-1 text-center">涨跌幅</div>
          </div>

          <!-- 列表项 -->
          <div class="max-h-[calc(100vh-220px)]">
            <div
              v-for="(item, index) in displayList"
              :key="index"
              class="flex items-center py-3 px-4 text-sm hover:bg-blue-50/30 transition-all border-l-2 relative"
              :class="{
                'border-l-green-500': isPositive(item.change),
                'border-l-red-500': isNegative(item.change),
                'border-l-gray-300': isZero(item.change),
                'bg-blue-50/10': index % 2 === 0,
              }"
            >
              <!-- 商品名称 -->
              <div class="flex-1">
                <div class="font-medium text-gray-800 truncate flex items-center">
                  <text
                    class="w-1.5 h-1.5 rounded-full mr-2 flex-shrink-0"
                    :class="{
                      'bg-green-500': isPositive(item.change),
                      'bg-red-500': isNegative(item.change),
                      'bg-gray-300': isZero(item.change),
                    }"
                  >
                  </text>
                  {{ item.name }}
                </div>
              </div>

              <!-- 市场 -->
              <div class="flex-1 text-center">
                <div class="text-xs text-gray-500 truncate flex items-center justify-center">
                  {{ item.market }}
                </div>
              </div>

              <!-- 价格 -->
              <div class="flex-1 flex justify-center items-center">
                <ly-price-format :price="item.price" />
              </div>

              <!-- 涨跌幅 -->
              <div class="flex-1 text-center">
                <text
                  class="px-2 py-1 rounded-sm text-xs font-medium inline-block min-w-[60px]"
                  :class="{
                    'bg-green-100/80 text-green-700': isPositive(item.change),
                    'bg-red-100/80 text-red-700': isNegative(item.change),
                    'bg-gray-100/80 text-gray-600': isZero(item.change),
                  }"
                >
                  <i
                    class="mr-0.5"
                    :class="{
                      'i-mdi-trending-up': isPositive(item.change),
                      'i-mdi-trending-down': isNegative(item.change),
                      'i-mdi-trending-neutral': isZero(item.change),
                    }"
                  >
                  </i>
                  {{ isPositive(item.change) ? '+' : '' }}{{ item.change.replace(/[+\-]/, '') }}
                </text>
              </div>

              <!-- 底部分隔线 -->
              <div class="absolute bottom-0 left-0 right-0 h-[1px] bg-gray-100"></div>
            </div>
          </div>
        </div>

        <!-- 无数据提示 -->
        <div v-if="displayList.length === 0" class="bg-white rounded-lg p-8 text-center text-gray-500 border border-dashed border-gray-200 mt-2 shadow-sm">
          <div class="flex flex-col items-center justify-center">
            <i class="i-mdi-chart-line-variant text-5xl text-gray-200 mb-2"></i>
            <p class="text-sm">暂无相关价格行情数据</p>
            <p class="text-xs text-gray-400 mt-1">可尝试切换分类或调整搜索条件</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部固定区域 -->
    <div class="flex-shrink-0 p-3 pt-1 bg-white border-t border-gray-100">
      <!-- 数据统计提示 -->
      <div v-if="displayList.length > 0" class="text-xs text-gray-400 text-right pr-1">共 {{ displayList.length }} 条数据</div>

      <!-- 数据更新提示 -->
      <div class="text-xs text-gray-400 text-center mt-1 flex items-center justify-center">
        <i class="i-mdi-clock-outline mr-1"></i>
        <text>更新时间：{{ formatDate(new Date()) }}</text>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';

  // 搜索相关
  const searchValue = ref('');

  // Tab 切换相关
  const tabIndex = ref(0);
  const onTabChange = (e: { currentIndex: number }) => {
    tabIndex.value = e.currentIndex;
  };

  // 分类相关
  const categories = ref(['全部', '粮食', '蔬菜', '水果', '畜禽', '水产']);
  const currentCategory = ref(0);

  // 选择分类
  const selectCategory = (index: number) => {
    currentCategory.value = index;
  };

  // 平台指导价数据
  const priceList = ref([
    {
      name: '小麦',
      market: '河南周口市场',
      price: '2.80',
      change: '+5.2%',
      category: '粮食',
    },
    {
      name: '玉米',
      market: '山东临沂市场',
      price: '2.15',
      change: '-1.5%',
      category: '粮食',
    },
    {
      name: '大豆',
      market: '黑龙江哈尔滨',
      price: '5.42',
      change: '+0.8%',
      category: '粮食',
    },
    {
      name: '土豆',
      market: '内蒙古乌兰察布',
      price: '2.0',
      change: '+0.0%',
      category: '蔬菜',
    },
    {
      name: '胡萝卜',
      market: '山东寿光市场',
      price: '1.8',
      change: '+3.5%',
      category: '蔬菜',
    },
    {
      name: '白菜',
      market: '河北唐山市场',
      price: '1.2',
      change: '-2.5%',
      category: '蔬菜',
    },
    {
      name: '苹果',
      market: '陕西咸阳市场',
      price: '5.5',
      change: '+4.8%',
      category: '水果',
    },
    {
      name: '香蕉',
      market: '广西南宁市场',
      price: '3.8',
      change: '-1.8%',
      category: '水果',
    },
  ]);

  // 商品成交价数据
  const transactionList = ref([
    {
      name: '小麦',
      market: '全国平均',
      price: '2.95',
      change: '+3.5%',
      category: '粮食',
    },
    {
      name: '玉米',
      market: '全国平均',
      price: '2.25',
      change: '-0.8%',
      category: '粮食',
    },
    {
      name: '大豆',
      market: '全国平均',
      price: '5.55',
      change: '+1.2%',
      category: '粮食',
    },
    {
      name: '土豆',
      market: '全国平均',
      price: '2.15',
      change: '+1.4%',
      category: '蔬菜',
    },
    {
      name: '胡萝卜',
      market: '全国平均',
      price: '1.95',
      change: '+2.6%',
      category: '蔬菜',
    },
    {
      name: '白菜',
      market: '全国平均',
      price: '1.35',
      change: '-1.5%',
      category: '蔬菜',
    },
    {
      name: '苹果',
      market: '全国平均',
      price: '5.85',
      change: '+3.5%',
      category: '水果',
    },
    {
      name: '香蕉',
      market: '全国平均',
      price: '4.05',
      change: '-0.7%',
      category: '水果',
    },
  ]);

  // 根据分类和搜索词筛选指导价数据
  const filteredPriceList = computed(() => {
    let result = priceList.value;

    // 按分类筛选
    if (currentCategory.value > 0) {
      result = result.filter((item) => item.category === categories.value[currentCategory.value]);
    }

    // 按搜索词筛选
    if (searchValue.value.trim()) {
      const keyword = searchValue.value.toLowerCase();
      result = result.filter((item) => item.name.toLowerCase().includes(keyword) || item.market.toLowerCase().includes(keyword));
    }

    return result;
  });

  // 根据分类和搜索词筛选成交价数据
  const filteredTransactionList = computed(() => {
    let result = transactionList.value;

    // 按分类筛选
    if (currentCategory.value > 0) {
      result = result.filter((item) => item.category === categories.value[currentCategory.value]);
    }

    // 按搜索词筛选
    if (searchValue.value.trim()) {
      const keyword = searchValue.value.toLowerCase();
      result = result.filter((item) => item.name.toLowerCase().includes(keyword) || item.market.toLowerCase().includes(keyword));
    }

    return result;
  });

  // 根据当前选中的 Tab 显示对应的列表数据
  const displayList = computed(() => {
    return tabIndex.value === 0 ? filteredPriceList.value : filteredTransactionList.value;
  });

  // 判断涨跌幅是否为正数、负数或零
  const isPositive = (change: string) => change.startsWith('+') && change !== '+0.00%' && change !== '+0.0%';
  const isNegative = (change: string) => change.startsWith('-') && change !== '-0.00%' && change !== '-0.0%';
  const isZero = (change: string) => change === '+0.00%' || change === '-0.00%' || change === '0.00%' || change === '+0.0%' || change === '-0.0%' || change === '0.0%';

  // 格式化日期
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  };
</script>

<style lang="scss" scoped>
  .hide-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }
</style>
